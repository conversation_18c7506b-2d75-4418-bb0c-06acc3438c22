

/**
 * AuthContext - Modular Authentication System
 *
 * REFACTORED: This file now serves as the main entry point for the modular
 * authentication system. All functionality has been organized into focused
 * modules while maintaining 100% backward compatibility.
 *
 * Created: Original AuthContext
 * Refactored: 2025-01-11
 * Part of: Authentication System Refactoring
 */

// Re-export all types and interfaces
export * from './AuthContext/types';

// Re-export the main provider and hook
export { AuthProvider, useAuth } from './AuthContext/index';






  // Store the last known user ID to detect genuine sign-ins vs refreshes
  const [lastKnownUserId, setLastKnownUserId] = useState<string | null>(null);

  // Main auth session effect - only depends on navigate
  useEffect(() => {
    // Track if this is the initial session fetch
    let isInitialMount = true;

    const fetchSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);

      if (session?.user) {
        setUser(session?.user);
        // Store the user ID when we first load the session
        setLastKnownUserId(session.user.id);
      }

      setLoading(false);
    };

    fetchSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Only update session if it's a meaningful change
        setSession(session);

        if (session?.user) {
          setUser(session?.user);

          // Only show welcome message and navigate on a genuine new sign-in
          // This happens when the user ID changes from null to a value
          const isGenuineSignIn = event === 'SIGNED_IN' &&
                                !isInitialMount &&
                                lastKnownUserId === null;

          if (isGenuineSignIn) {
            toast.success(`Welcome back!`);
            navigate('/book-club');
            // Update the last known user ID
            setLastKnownUserId(session.user.id);
          } else if (lastKnownUserId !== session.user.id) {
            // Update the last known user ID without navigation
            // This handles user changes without tab switching
            setLastKnownUserId(session.user.id);
          }
        } else {
          // User signed out
          setUser(null);
          setClubRoles({});
          setLastKnownUserId(null);
          // Clear subscription data on sign out
          setSubscriptionStatus(null);
          setSubscriptionLoading(false);
        }

        setLoading(false);
        // After the first auth state change, we're no longer in the initial mount
        isInitialMount = false;
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [navigate, lastKnownUserId]); // Added lastKnownUserId as a dependency

  // Separate effect for fetching club roles when user changes
  useEffect(() => {
    if (user?.id) {
      fetchClubRoles();
    }
  }, [user?.id]);

  // Effect for loading entitlements when user changes
  useEffect(() => {
    if (user?.id) {
      // Set loading state
      setEntitlementsLoading(true);

      // Use the cached entitlements if available
      getUserEntitlements(user.id)
        .then(userEntitlements => {
          setEntitlements(userEntitlements);
        })
        .catch(() => {
          setEntitlements([]);
        })
        .finally(() => {
          setEntitlementsLoading(false);
        });
    } else {
      setEntitlements([]);
      setEntitlementsLoading(false);
    }
  }, [user?.id]);

  // Effect for loading subscription status when user changes (Phase 4B.1.1)
  useEffect(() => {
    if (user?.id) {
      // Load subscription status asynchronously (don't block authentication)
      refreshSubscriptionStatus().catch(error => {
        console.error('[AuthContext] Failed to load subscription status on user change:', error);
        // Graceful degradation - subscription data is optional
      });
    } else {
      setSubscriptionStatus(null);
      setSubscriptionLoading(false);
    }
  }, [user?.id]);

  const signIn = async (email: string, password: string) => {
    setLoading(true);

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast.error(error.message || "Failed to sign in");
        return;
      }

      toast.success("Successfully signed in!");

      // Navigate will be handled by the auth state change listener
    } catch (error: any) {
      toast.error(error.message || "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, username: string) => {
    setLoading(true);

    try {
      // Create auth user with username in metadata for database trigger
      const { error, data } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username
          }
        }
      });

      if (error) {
        toast.error(error.message || "Failed to sign up");
        return;
      }

      if (data.user) {
        toast.success("Account created! Welcome to BookConnect!");
        navigate('/book-club');
      }
    } catch (error: any) {
      toast.error(error.message || "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      // Invalidate caches if user exists
      if (user?.id) {
        invalidateUserEntitlements(user.id);

        // Invalidate subscription cache using Phase 4A.1 integration
        try {
          await invalidateOnSubscriptionEvent(user.id, 'subscription_expired');
        } catch (error) {
          console.warn('[AuthContext] Failed to invalidate subscription cache on sign out:', error);
          // Non-critical error - continue with sign out
        }
      }

      await supabase.auth.signOut();
      setEntitlements([]);
      setSubscriptionStatus(null);
      toast.success("You've been successfully signed out");
      navigate('/login');
    } catch (error) {
      toast.error("Failed to sign out");
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signIn,
      signUp,
      signOut,
      clubRoles,
      fetchClubRoles,
      isAdmin,
      isMember,
      entitlements,
      entitlementsLoading,
      refreshEntitlements,
      hasEntitlement: checkEntitlement,
      hasContextualEntitlement: checkContextualEntitlement,
      // Subscription state (Phase 4B.1.1)
      subscriptionStatus,
      subscriptionLoading,
      refreshSubscriptionStatus,
      hasValidSubscription,
      getSubscriptionTier,
      hasRequiredTier,
      // Enhanced subscription helpers (Phase 4B.1.2)
      canAccessFeature,
      getSubscriptionStatusWithContext,
      // Coordinated data refresh (Phase 4B.1.2)
      refreshUserData
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
